import{applyProps as i,addEffect as e}from"@react-three/fiber";import{Globals as m}from"@react-spring/core";import{createStringInterpolator as a,colors as p,raf as s}from"@react-spring/shared";import{createHost as n}from"@react-spring/animated";import*as r from"three";import"@react-three/fiber";var o=["primitive"].concat(Object.keys(r).filter(t=>/^[A-Z]/.test(t)).map(t=>t[0].toLowerCase()+t.slice(1)));export*from"@react-spring/core";m.assign({createStringInterpolator:a,colors:p,frameLoop:"demand"});e(()=>{s.advance()});var f=n(o,{applyAnimatedValues:i}),E=f.animated;export{E as a,E as animated};
