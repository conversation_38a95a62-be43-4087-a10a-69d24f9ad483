import { useRef, useMemo } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Points, PointMaterial } from '@react-three/drei'
import * as THREE from 'three'

function StarField({ count = 5000 }) {
  const mesh = useRef()
  
  const particles = useMemo(() => {
    const temp = new Float32Array(count * 3)
    for (let i = 0; i < count; i++) {
      const i3 = i * 3
      temp[i3] = (Math.random() - 0.5) * 100
      temp[i3 + 1] = (Math.random() - 0.5) * 100
      temp[i3 + 2] = (Math.random() - 0.5) * 100
    }
    return temp
  }, [count])

  useFrame((state, delta) => {
    if (mesh.current) {
      mesh.current.rotation.x -= delta / 10
      mesh.current.rotation.y -= delta / 15
    }
  })

  return (
    <group rotation={[0, 0, Math.PI / 4]}>
      <Points ref={mesh} positions={particles} stride={3} frustumCulled={false}>
        <PointMaterial
          transparent
          color="#00f5ff"
          size={0.05}
          sizeAttenuation={true}
          depthWrite={false}
        />
      </Points>
    </group>
  )
}

function FloatingCubes() {
  const mesh = useRef()
  
  useFrame((state, delta) => {
    if (mesh.current) {
      mesh.current.rotation.x += delta * 0.2
      mesh.current.rotation.y += delta * 0.3
      mesh.current.position.y = Math.sin(state.clock.elapsedTime) * 0.5
    }
  })

  return (
    <mesh ref={mesh} position={[10, 0, -10]}>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial 
        color="#8b5cf6" 
        transparent 
        opacity={0.3}
        wireframe
      />
    </mesh>
  )
}

function NetworkNodes() {
  const groupRef = useRef()
  
  const nodes = useMemo(() => {
    return Array.from({ length: 20 }, (_, i) => ({
      position: [
        (Math.random() - 0.5) * 50,
        (Math.random() - 0.5) * 50,
        (Math.random() - 0.5) * 50
      ],
      color: ['#00f5ff', '#8b5cf6', '#10b981', '#f97316'][Math.floor(Math.random() * 4)]
    }))
  }, [])

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.1
    }
  })

  return (
    <group ref={groupRef}>
      {nodes.map((node, i) => (
        <mesh key={i} position={node.position}>
          <sphereGeometry args={[0.1, 8, 8]} />
          <meshBasicMaterial color={node.color} />
        </mesh>
      ))}
    </group>
  )
}

function DataStreams() {
  const linesRef = useRef()
  
  const points = useMemo(() => {
    const curves = []
    for (let i = 0; i < 10; i++) {
      const curve = new THREE.CatmullRomCurve3([
        new THREE.Vector3(-20 + Math.random() * 40, -10, -10),
        new THREE.Vector3(-10 + Math.random() * 20, 0, 0),
        new THREE.Vector3(-20 + Math.random() * 40, 10, 10),
      ])
      curves.push(curve.getPoints(50))
    }
    return curves
  }, [])

  useFrame((state) => {
    if (linesRef.current) {
      linesRef.current.children.forEach((line, i) => {
        line.material.opacity = 0.3 + Math.sin(state.clock.elapsedTime + i) * 0.2
      })
    }
  })

  return (
    <group ref={linesRef}>
      {points.map((curve, i) => (
        <line key={i}>
          <bufferGeometry>
            <bufferAttribute
              attach="attributes-position"
              count={curve.length}
              array={new Float32Array(curve.flatMap(p => [p.x, p.y, p.z]))}
              itemSize={3}
            />
          </bufferGeometry>
          <lineBasicMaterial color="#00f5ff" transparent opacity={0.3} />
        </line>
      ))}
    </group>
  )
}

function CameraController() {
  useFrame((state) => {
    state.camera.position.x = Math.sin(state.clock.elapsedTime * 0.1) * 5
    state.camera.position.z = Math.cos(state.clock.elapsedTime * 0.1) * 5
    state.camera.lookAt(0, 0, 0)
  })
  return null
}

const ThreeBackground = ({ isNightMode, weatherEffect }) => {
  return (
    <div className="three-background">
      <Canvas
        camera={{ position: [0, 0, 30], fov: 60 }}
        style={{ position: 'absolute', top: 0, left: 0, zIndex: -1 }}
      >
        <ambientLight intensity={isNightMode ? 0.2 : 0.4} />
        <pointLight position={[10, 10, 10]} intensity={isNightMode ? 0.8 : 0.5} color="#00f5ff" />
        <pointLight position={[-10, -10, -10]} intensity={0.3} color="#8b5cf6" />
        
        <StarField count={isNightMode ? 8000 : 3000} />
        <FloatingCubes />
        <NetworkNodes />
        <DataStreams />
        <CameraController />
        
        {weatherEffect === 'fog' && (
          <fog attach="fog" args={['#1a1a2e', 10, 100]} />
        )}
      </Canvas>
    </div>
  )
}

export default ThreeBackground
