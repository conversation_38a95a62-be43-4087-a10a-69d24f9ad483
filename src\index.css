/* Custom CSS Variables for the futuristic theme */

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');

:root {
  font-family: 'Exo 2', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #0a0a0a;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Custom color variables */
  --cyber-blue: #00f5ff;
  --cyber-purple: #8b5cf6;
  --cyber-pink: #f472b6;
  --cyber-green: #10b981;
  --cyber-orange: #f97316;
  --neon-yellow: #fbbf24;
  --dark-bg: #0a0a0a;
  --dark-surface: #1a1a1a;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00f5ff, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #8b5cf6, #f472b6);
}

/* Isometric perspective utilities */
.isometric {
  transform: rotateX(60deg) rotateY(-45deg) rotateZ(0deg);
  transform-style: preserve-3d;
}

.isometric-container {
  perspective: 1500px;
  perspective-origin: center center;
}

/* Glowing effects */
.glow-blue {
  box-shadow: 0 0 10px #00f5ff, 0 0 20px #00f5ff, 0 0 30px #00f5ff;
}

.glow-purple {
  box-shadow: 0 0 10px #8b5cf6, 0 0 20px #8b5cf6, 0 0 30px #8b5cf6;
}

.glow-green {
  box-shadow: 0 0 10px #10b981, 0 0 20px #10b981, 0 0 30px #10b981;
}

/* Data flow animation */
@keyframes dataFlow {
  0% { transform: translateX(-100%) translateY(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateX(100%) translateY(0); opacity: 0; }
}

.data-flow {
  animation: dataFlow 3s linear infinite;
}

/* Building hover effects */
.building-hover {
  transition: all 0.3s ease;
}

.building-hover:hover {
  transform: translateY(-5px) scale(1.05);
  filter: brightness(1.2);
}

/* Header styles */
.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(0, 245, 255, 0.2);
}

/* Utility classes */
.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.max-w-7xl { max-width: 80rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.text-white { color: white; }
.text-gray-400 { color: #9ca3af; }
.text-gray-300 { color: #d1d5db; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }
.min-h-screen { min-height: 100vh; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }
.cursor-pointer { cursor: pointer; }
.transition-all { transition: all 0.3s ease; }
.transition-colors { transition: color 0.3s ease; }
.hover\:scale-110:hover { transform: scale(1.1); }
.hover\:scale-105:hover { transform: scale(1.05); }

/* Color classes */
.text-cyber-blue { color: var(--cyber-blue); }
.text-cyber-purple { color: var(--cyber-purple); }
.text-cyber-green { color: var(--cyber-green); }
.text-cyber-orange { color: var(--cyber-orange); }
.text-cyber-pink { color: var(--cyber-pink); }
.text-neon-yellow { color: var(--neon-yellow); }
.bg-dark-bg { background-color: var(--dark-bg); }
.bg-dark-surface { background-color: var(--dark-surface); }

/* Cyber theme specific classes */
.cyber-blue { color: var(--cyber-blue); }
.cyber-purple { color: var(--cyber-purple); }
.cyber-green { color: var(--cyber-green); }
.cyber-orange { color: var(--cyber-orange); }
.cyber-pink { color: var(--cyber-pink); }
.neon-yellow { color: var(--neon-yellow); }
