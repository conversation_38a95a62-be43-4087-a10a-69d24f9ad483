import { useRef, useMemo } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Text, Sphere, MeshDistortMaterial } from '@react-three/drei'
import * as THREE from 'three'

function SkillOrb({ position, skill, color, index }) {
  const meshRef = useRef()
  const textRef = useRef()
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime + index) * 0.2
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.5
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.5
    }
    
    if (textRef.current) {
      textRef.current.lookAt(state.camera.position)
    }
  })

  return (
    <group position={position}>
      <Sphere ref={meshRef} args={[1, 32, 32]}>
        <MeshDistortMaterial
          color={color}
          transparent
          opacity={0.8}
          distort={0.3}
          speed={2}
          roughness={0.4}
        />
      </Sphere>
      <Text
        ref={textRef}
        position={[0, 0, 1.5]}
        fontSize={0.5}
        color="white"
        anchorX="center"
        anchorY="middle"
        font="/fonts/inter-bold.woff"
      >
        {skill.name}
      </Text>
      <Text
        position={[0, -0.5, 1.5]}
        fontSize={0.3}
        color="#00f5ff"
        anchorX="center"
        anchorY="middle"
      >
        {skill.level}%
      </Text>
    </group>
  )
}

function ConnectionLines({ skills }) {
  const linesRef = useRef()
  
  const connections = useMemo(() => {
    const lines = []
    for (let i = 0; i < skills.length; i++) {
      for (let j = i + 1; j < skills.length; j++) {
        if (Math.random() > 0.7) { // Random connections
          lines.push([skills[i].position, skills[j].position])
        }
      }
    }
    return lines
  }, [skills])

  useFrame((state) => {
    if (linesRef.current) {
      linesRef.current.children.forEach((line, i) => {
        line.material.opacity = 0.2 + Math.sin(state.clock.elapsedTime + i) * 0.1
      })
    }
  })

  return (
    <group ref={linesRef}>
      {connections.map((connection, i) => {
        const points = [
          new THREE.Vector3(...connection[0]),
          new THREE.Vector3(...connection[1])
        ]
        const geometry = new THREE.BufferGeometry().setFromPoints(points)
        
        return (
          <line key={i} geometry={geometry}>
            <lineBasicMaterial color="#00f5ff" transparent opacity={0.2} />
          </line>
        )
      })}
    </group>
  )
}

function CentralCore() {
  const coreRef = useRef()
  
  useFrame((state) => {
    if (coreRef.current) {
      coreRef.current.rotation.x = state.clock.elapsedTime * 0.3
      coreRef.current.rotation.y = state.clock.elapsedTime * 0.2
      coreRef.current.rotation.z = state.clock.elapsedTime * 0.1
    }
  })

  return (
    <group ref={coreRef}>
      <Sphere args={[0.5, 32, 32]}>
        <MeshDistortMaterial
          color="#8b5cf6"
          transparent
          opacity={0.6}
          distort={0.5}
          speed={3}
          emissive="#8b5cf6"
          emissiveIntensity={0.2}
        />
      </Sphere>
      
      {/* Orbiting rings */}
      {[1, 1.5, 2].map((radius, i) => (
        <mesh key={i} rotation={[Math.PI / 2, 0, 0]}>
          <torusGeometry args={[radius, 0.02, 8, 32]} />
          <meshBasicMaterial color="#00f5ff" transparent opacity={0.3} />
        </mesh>
      ))}
    </group>
  )
}

const SkillSphere = ({ skills = [] }) => {
  const skillsWithPositions = useMemo(() => {
    return skills.map((skill, index) => {
      const angle = (index / skills.length) * Math.PI * 2
      const radius = 5 + Math.random() * 3
      const height = (Math.random() - 0.5) * 4
      
      return {
        ...skill,
        position: [
          Math.cos(angle) * radius,
          height,
          Math.sin(angle) * radius
        ]
      }
    })
  }, [skills])

  const colors = ['#00f5ff', '#8b5cf6', '#10b981', '#f97316', '#ef4444', '#06b6d4']

  return (
    <div className="skill-sphere-container" style={{ height: '600px', width: '100%' }}>
      <Canvas camera={{ position: [0, 0, 15], fov: 60 }}>
        <ambientLight intensity={0.4} />
        <pointLight position={[10, 10, 10]} intensity={1} color="#00f5ff" />
        <pointLight position={[-10, -10, -10]} intensity={0.5} color="#8b5cf6" />
        
        <CentralCore />
        
        {skillsWithPositions.map((skill, index) => (
          <SkillOrb
            key={skill.name}
            position={skill.position}
            skill={skill}
            color={colors[index % colors.length]}
            index={index}
          />
        ))}
        
        <ConnectionLines skills={skillsWithPositions} />
        
        {/* Camera controller */}
        <CameraController />
      </Canvas>
    </div>
  )
}

function CameraController() {
  useFrame((state) => {
    const time = state.clock.elapsedTime
    state.camera.position.x = Math.sin(time * 0.2) * 8
    state.camera.position.z = Math.cos(time * 0.2) * 8
    state.camera.lookAt(0, 0, 0)
  })
  return null
}

export default SkillSphere
