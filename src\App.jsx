import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import './App.css'

function App() {
  const [isLoaded, setIsLoaded] = useState(false)
  const [currentSection, setCurrentSection] = useState('overview')
  const [selectedBuilding, setSelectedBuilding] = useState(null)
  const [cityScale, setCityScale] = useState(1)
  const [cityPosition, setCityPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [isNightMode, setIsNightMode] = useState(false)
  const [weatherEffect, setWeatherEffect] = useState('clear')
  const [performanceMode, setPerformanceMode] = useState(false)

  useEffect(() => {
    // Simulate loading time for dramatic effect
    const timer = setTimeout(() => setIsLoaded(true), 1000)
    return () => clearTimeout(timer)
  }, [])

  // Handle zoom with mouse wheel
  useEffect(() => {
    const handleWheel = (e) => {
      e.preventDefault()
      const delta = e.deltaY > 0 ? 0.9 : 1.1
      setCityScale(prev => Math.max(0.5, Math.min(2, prev * delta)))
    }

    const cityContainer = document.getElementById('city-container')
    if (cityContainer) {
      cityContainer.addEventListener('wheel', handleWheel, { passive: false })
      return () => cityContainer.removeEventListener('wheel', handleWheel)
    }
  }, [])

  // Handle drag functionality
  const handleMouseDown = (e) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX - cityPosition.x, y: e.clientY - cityPosition.y })
  }

  const handleMouseMove = (e) => {
    if (isDragging) {
      setCityPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, dragStart, cityPosition.x, cityPosition.y])

  // Handle section navigation
  const handleSectionChange = (section) => {
    setCurrentSection(section)
    // Animate to specific city areas based on section
    switch (section) {
      case 'skills':
        setCityPosition({ x: -100, y: -50 })
        setCityScale(1.2)
        break
      case 'projects':
        setCityPosition({ x: 0, y: -100 })
        setCityScale(1.1)
        break
      case 'education':
        setCityPosition({ x: 100, y: 50 })
        setCityScale(1.3)
        break
      case 'contact':
        setCityPosition({ x: -50, y: 100 })
        setCityScale(1.4)
        break
      default:
        setCityPosition({ x: 0, y: 0 })
        setCityScale(1)
    }
  }

  // Day/Night cycle effect
  useEffect(() => {
    const interval = setInterval(() => {
      setIsNightMode(prev => !prev)
    }, 30000) // Change every 30 seconds for demo
    return () => clearInterval(interval)
  }, [])

  // Weather effects cycle
  useEffect(() => {
    const weatherTypes = ['clear', 'rain', 'snow', 'fog']
    const interval = setInterval(() => {
      setWeatherEffect(weatherTypes[Math.floor(Math.random() * weatherTypes.length)])
    }, 45000) // Change every 45 seconds
    return () => clearInterval(interval)
  }, [])

  // Performance optimization - detect low-end devices
  useEffect(() => {
    const checkPerformance = () => {
      const canvas = document.createElement('canvas')
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      if (!gl) {
        setPerformanceMode(true)
        return
      }

      // Check for reduced motion preference
      if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        setPerformanceMode(true)
      }

      // Check device memory (if available)
      if (navigator.deviceMemory && navigator.deviceMemory < 4) {
        setPerformanceMode(true)
      }
    }

    checkPerformance()
  }, [])

  if (!isLoaded) {
    return (
      <div className="loading-screen">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="loading-content"
        >
          <div className="loading-title">
            VINAYAK B S
          </div>
          <div className="loading-subtitle">
            Initializing Digital Metropolis...
          </div>
          <div className="loading-bar-container">
            <motion.div
              className="loading-bar"
              initial={{ width: 0 }}
              animate={{ width: '100%' }}
              transition={{ duration: 2, ease: "easeInOut" }}
            />
          </div>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="app-container">
      <header className="header-container">
        <div className="header-content">
          <div className="header-left">
            <div className="logo-text">VINAYAK B S</div>
            <div className="subtitle">Full Stack Developer</div>
          </div>
          <nav className="nav-container">
            {['Overview', 'Skills', 'Projects', 'Education', 'Contact'].map((item) => (
              <motion.button
                key={item}
                onClick={() => handleSectionChange(item.toLowerCase())}
                className={`nav-button ${currentSection === item.toLowerCase() ? 'active' : ''}`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {item}
              </motion.button>
            ))}
          </nav>
        </div>
      </header>

      <main className="main-content">
        <motion.div
          id="city-container"
          className={`city-container ${isNightMode ? 'night-mode' : 'day-mode'} weather-${weatherEffect}`}
          onMouseDown={handleMouseDown}
          style={{
            cursor: isDragging ? 'grabbing' : 'grab'
          }}
          animate={{
            scale: cityScale,
            x: cityPosition.x,
            y: cityPosition.y
          }}
          transition={{
            type: "spring",
            stiffness: 100,
            damping: 20
          }}
        >
          {/* Weather Effects */}
          {weatherEffect === 'rain' && (
            <div className="weather-effect rain-effect">
              {[...Array(50)].map((_, i) => (
                <motion.div
                  key={i}
                  className="rain-drop"
                  initial={{
                    x: Math.random() * window.innerWidth,
                    y: -10,
                    opacity: 0.7
                  }}
                  animate={{
                    y: window.innerHeight + 10,
                    opacity: 0
                  }}
                  transition={{
                    duration: 1 + Math.random(),
                    repeat: Infinity,
                    delay: Math.random() * 2,
                    ease: "linear"
                  }}
                />
              ))}
            </div>
          )}

          {weatherEffect === 'snow' && (
            <div className="weather-effect snow-effect">
              {[...Array(30)].map((_, i) => (
                <motion.div
                  key={i}
                  className="snow-flake"
                  initial={{
                    x: Math.random() * window.innerWidth,
                    y: -10,
                    opacity: 0.8
                  }}
                  animate={{
                    y: window.innerHeight + 10,
                    x: Math.random() * window.innerWidth,
                    opacity: 0
                  }}
                  transition={{
                    duration: 3 + Math.random() * 2,
                    repeat: Infinity,
                    delay: Math.random() * 3,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </div>
          )}

          {weatherEffect === 'fog' && (
            <motion.div
              className="weather-effect fog-effect"
              animate={{
                opacity: [0.3, 0.7, 0.3]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          )}
          <div className="city-grid">
            {/* Row 1 - Frontend & Languages */}
            <div className="building react-building" onClick={() => setSelectedBuilding('react')}>
              <div className="building-content">
                <div className="building-icon">⚛️</div>
                <div className="building-label">React</div>
              </div>
            </div>

            <div className="building javascript-building" onClick={() => setSelectedBuilding('javascript')}>
              <div className="building-content">
                <div className="building-icon">📜</div>
                <div className="building-label">JavaScript</div>
              </div>
            </div>

            <div className="building python-building" onClick={() => setSelectedBuilding('python')}>
              <div className="building-content">
                <div className="building-icon">🐍</div>
                <div className="building-label">Python</div>
              </div>
            </div>

            <div className="building cpp-building" onClick={() => setSelectedBuilding('cpp')}>
              <div className="building-content">
                <div className="building-icon">⚙️</div>
                <div className="building-label">C++</div>
              </div>
            </div>

            {/* Row 2 - Backend & Databases */}
            <div className="building node-building" onClick={() => setSelectedBuilding('node')}>
              <div className="building-content">
                <div className="building-icon">🟢</div>
                <div className="building-label">Node.js</div>
              </div>
            </div>

            <div className="building mongodb-building" onClick={() => setSelectedBuilding('mongodb')}>
              <div className="building-content">
                <div className="building-icon">🍃</div>
                <div className="building-label">MongoDB</div>
              </div>
            </div>

            <div className="building firebase-building" onClick={() => setSelectedBuilding('firebase')}>
              <div className="building-content">
                <div className="building-icon">🔥</div>
                <div className="building-label">Firebase</div>
              </div>
            </div>

            <div className="building git-building" onClick={() => setSelectedBuilding('git')}>
              <div className="building-content">
                <div className="building-icon">📚</div>
                <div className="building-label">Git</div>
              </div>
            </div>

            {/* Row 3 - Projects */}
            <div className="building project-building tall" onClick={() => setSelectedBuilding('zerohunger')}>
              <div className="building-content">
                <div className="building-icon">🍽️</div>
                <div className="building-label">ZeroHunger</div>
              </div>
            </div>

            <div className="building project-building medium" onClick={() => setSelectedBuilding('localtasker')}>
              <div className="building-content">
                <div className="building-icon">🏠</div>
                <div className="building-label">LocalTasker</div>
              </div>
            </div>

            <div className="building project-building" onClick={() => setSelectedBuilding('ai-interview')}>
              <div className="building-content">
                <div className="building-icon">🎤</div>
                <div className="building-label">AI Interview</div>
              </div>
            </div>

            <div className="building achievement-building" onClick={() => setSelectedBuilding('achievements')}>
              <div className="building-content">
                <div className="building-icon">🏆</div>
                <div className="building-label">Achievements</div>
              </div>
            </div>

            {/* Row 4 - Education & Contact */}
            <div className="building education-building" onClick={() => setSelectedBuilding('education')}>
              <div className="building-content">
                <div className="building-icon">🎓</div>
                <div className="building-label">SIT University</div>
              </div>
            </div>

            <div className="building empty-space"></div>

            <div className="building empty-space"></div>

            <div className="building contact-building tallest" onClick={() => setSelectedBuilding('contact')}>
              <div className="building-content">
                <div className="building-icon">📡</div>
                <div className="building-label">Contact</div>
              </div>
            </div>
          </div>

          {/* Data Flow Lines */}
          <div className="data-flows">
            {/* Horizontal connections */}
            <div className="data-line horizontal line-1"></div>
            <div className="data-line horizontal line-2"></div>
            <div className="data-line horizontal line-3"></div>

            {/* Vertical connections */}
            <div className="data-line vertical line-4"></div>
            <div className="data-line vertical line-5"></div>

            {/* Diagonal connections */}
            <div className="data-line diagonal line-6"></div>
            <div className="data-line diagonal line-7"></div>

            {/* Floating data packets */}
            <div className="data-packets">
              {[...Array(12)].map((_, i) => (
                <motion.div
                  key={i}
                  className="data-packet"
                  initial={{
                    x: Math.random() * 600,
                    y: Math.random() * 400,
                    opacity: 0
                  }}
                  animate={{
                    x: Math.random() * 600,
                    y: Math.random() * 400,
                    opacity: [0, 1, 0]
                  }}
                  transition={{
                    duration: 4 + Math.random() * 2,
                    repeat: Infinity,
                    delay: i * 0.5,
                    ease: "linear"
                  }}
                />
              ))}
            </div>

            {/* Network pulse effect */}
            <motion.div
              className="network-pulse"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.3, 0.8, 0.3]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </motion.div>

        {/* Navigation Controls */}
        <div className="controls-container">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setCityScale(prev => Math.min(2, prev * 1.2))}
            className="control-button zoom-in"
            title="Zoom In"
          >
            +
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setCityScale(prev => Math.max(0.5, prev * 0.8))}
            className="control-button zoom-out"
            title="Zoom Out"
          >
            -
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => {
              setCityScale(1)
              setCityPosition({ x: 0, y: 0 })
              setCurrentSection('overview')
            }}
            className="control-button reset"
            title="Reset View"
          >
            ⌂
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setIsNightMode(!isNightMode)}
            className={`control-button day-night ${isNightMode ? 'night' : 'day'}`}
            title={isNightMode ? 'Switch to Day' : 'Switch to Night'}
          >
            {isNightMode ? '☀️' : '🌙'}
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => {
              const weathers = ['clear', 'rain', 'snow', 'fog']
              const currentIndex = weathers.indexOf(weatherEffect)
              const nextIndex = (currentIndex + 1) % weathers.length
              setWeatherEffect(weathers[nextIndex])
            }}
            className="control-button weather"
            title={`Current: ${weatherEffect}`}
          >
            {weatherEffect === 'clear' && '☀️'}
            {weatherEffect === 'rain' && '🌧️'}
            {weatherEffect === 'snow' && '❄️'}
            {weatherEffect === 'fog' && '🌫️'}
          </motion.button>
        </div>

        {/* Mini Map */}
        <div className="mini-map">
          <div className="mini-map-title">CITY MAP</div>
          <div className="mini-map-content">
            <div className="mini-map-grid">
              {[...Array(16)].map((_, i) => (
                <div
                  key={i}
                  className={`mini-map-cell ${Math.random() > 0.7 ? 'active' : ''}`}
                />
              ))}
            </div>
            <motion.div
              className="mini-map-viewport"
              animate={{
                x: `${50 + cityPosition.x / 10}%`,
                y: `${50 + cityPosition.y / 10}%`,
                scale: cityScale
              }}
            />
          </div>
        </div>

        {/* Building Details Modal */}
        {selectedBuilding && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="modal-overlay"
            onClick={() => setSelectedBuilding(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="modal-content"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                className="modal-close"
                onClick={() => setSelectedBuilding(null)}
              >
                ×
              </button>
              <div className="modal-body">
                {selectedBuilding === 'react' && (
                  <div>
                    <h3>⚛️ React Development</h3>
                    <p>Building modern, interactive frontend interfaces and user experiences with component-based architecture.</p>
                    <div className="skills-list">
                      <span>React.js</span>
                      <span>JSX</span>
                      <span>Hooks</span>
                      <span>State Management</span>
                      <span>Component Design</span>
                      <span>Virtual DOM</span>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'node' && (
                  <div>
                    <h3>🟢 Node.js Backend</h3>
                    <p>Server-side applications, APIs, and backend services using JavaScript runtime environment.</p>
                    <div className="skills-list">
                      <span>Node.js</span>
                      <span>Express.js</span>
                      <span>REST APIs</span>
                      <span>Authentication</span>
                      <span>Middleware</span>
                      <span>Server Architecture</span>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'mongodb' && (
                  <div>
                    <h3>🍃 MongoDB Database</h3>
                    <p>NoSQL database management, schema design, and data optimization for scalable applications.</p>
                    <div className="skills-list">
                      <span>MongoDB</span>
                      <span>Mongoose</span>
                      <span>Database Design</span>
                      <span>Aggregation</span>
                      <span>Indexing</span>
                      <span>Data Modeling</span>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'zerohunger' && (
                  <div>
                    <h3>🍽️ ZeroHunger - Food Donation Platform</h3>
                    <p>A comprehensive platform connecting food donors with NGOs to reduce food waste and help those in need. Features real-time matching and donation tracking.</p>
                    <div className="tech-stack">
                      <span>Node.js</span>
                      <span>Express.js</span>
                      <span>MongoDB</span>
                      <span>React.js</span>
                    </div>
                    <div className="project-features">
                      <h4>Key Features:</h4>
                      <ul>
                        <li>Real-time NGO selection and matching</li>
                        <li>Location-based service provider discovery</li>
                        <li>Donation tracking and analytics</li>
                        <li>User authentication and profiles</li>
                      </ul>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'localtasker' && (
                  <div>
                    <h3>🏠 LocalTasker - Neighborhood Services</h3>
                    <p>A neighborhood service marketplace connecting property owners with local service providers for various tasks and maintenance needs.</p>
                    <div className="tech-stack">
                      <span>React.js</span>
                      <span>Node.js</span>
                      <span>MongoDB</span>
                      <span>Express.js</span>
                    </div>
                    <div className="project-features">
                      <h4>Key Features:</h4>
                      <ul>
                        <li>Service marketplace with categories</li>
                        <li>User authentication and verification</li>
                        <li>Review and rating system</li>
                        <li>Booking and scheduling system</li>
                      </ul>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'ai-interview' && (
                  <div>
                    <h3>🎤 AI-Powered Mock Interview System</h3>
                    <p>An intelligent interview simulation platform providing real-time feedback and performance analytics using Google Gemini AI.</p>
                    <div className="tech-stack">
                      <span>React.js</span>
                      <span>Node.js</span>
                      <span>MongoDB</span>
                      <span>Google Gemini AI</span>
                    </div>
                    <div className="project-features">
                      <h4>Key Features:</h4>
                      <ul>
                        <li>AI-powered feedback and analysis</li>
                        <li>Performance analytics and scoring</li>
                        <li>Dynamic question generation</li>
                        <li>Interview simulation environments</li>
                      </ul>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'education' && (
                  <div>
                    <h3>🎓 Siddaganga Institute of Technology</h3>
                    <p>B.E. in Computer Science and Engineering (2022-2026)</p>
                    <div className="education-details">
                      <div className="academic-performance">
                        <h4>Academic Performance:</h4>
                        <ul>
                          <li>Current CGPA: 8.94</li>
                          <li>Pre University: 91.33%</li>
                          <li>SSLC Examination: 94.4%</li>
                        </ul>
                      </div>
                      <div className="institution-info">
                        <h4>Institution Details:</h4>
                        <ul>
                          <li>Location: Tumakuru, Karnataka</li>
                          <li>Duration: 2022 - 2026</li>
                          <li>Specialization: Computer Science & Engineering</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'javascript' && (
                  <div>
                    <h3>📜 JavaScript Mastery</h3>
                    <p>Modern JavaScript development with ES6+ features, asynchronous programming, and DOM manipulation.</p>
                    <div className="skills-list">
                      <span>JavaScript ES6+</span>
                      <span>Async/Await</span>
                      <span>DOM Manipulation</span>
                      <span>Event Handling</span>
                      <span>Promises</span>
                      <span>Modules</span>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'python' && (
                  <div>
                    <h3>🐍 Python Development</h3>
                    <p>Data structures, algorithms, automation, and problem-solving with Python programming language.</p>
                    <div className="skills-list">
                      <span>Python</span>
                      <span>Data Structures</span>
                      <span>Algorithms</span>
                      <span>Problem Solving</span>
                      <span>Automation</span>
                      <span>OOP</span>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'cpp' && (
                  <div>
                    <h3>⚙️ C++ Programming</h3>
                    <p>System programming, competitive programming, and performance-critical applications using C++.</p>
                    <div className="skills-list">
                      <span>C++</span>
                      <span>STL</span>
                      <span>Memory Management</span>
                      <span>Competitive Programming</span>
                      <span>Data Structures</span>
                      <span>Algorithms</span>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'firebase' && (
                  <div>
                    <h3>🔥 Firebase Services</h3>
                    <p>Cloud database, authentication, hosting, and real-time data synchronization with Firebase platform.</p>
                    <div className="skills-list">
                      <span>Firebase</span>
                      <span>Firestore</span>
                      <span>Authentication</span>
                      <span>Hosting</span>
                      <span>Real-time Database</span>
                      <span>Cloud Functions</span>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'git' && (
                  <div>
                    <h3>📚 Version Control</h3>
                    <p>Code versioning, collaboration, and project management using Git and GitHub platforms.</p>
                    <div className="skills-list">
                      <span>Git</span>
                      <span>GitHub</span>
                      <span>Version Control</span>
                      <span>Branching</span>
                      <span>Merging</span>
                      <span>Collaboration</span>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'achievements' && (
                  <div>
                    <h3>🏆 Achievements & Recognition</h3>
                    <p>Notable accomplishments, competitions, and certifications throughout my academic and professional journey.</p>
                    <div className="achievements-list">
                      <div className="achievement-item">
                        <h4>🥇 IBM Z Datathon Participant</h4>
                        <p>Participated in IBM's global hackathon event focusing on data analytics and innovation.</p>
                      </div>
                      <div className="achievement-item">
                        <h4>💻 GeeksforGeeks Problem Solver</h4>
                        <p>Solved 200+ coding problems and maintained consistent problem-solving streak.</p>
                      </div>
                      <div className="achievement-item">
                        <h4>🎬 Team Quest Video Editing Lead</h4>
                        <p>Led video editing team for college club, creating engaging content and tutorials.</p>
                      </div>
                      <div className="achievement-item">
                        <h4>📚 Academic Excellence</h4>
                        <p>Maintained CGPA of 8.94 throughout engineering studies with consistent performance.</p>
                      </div>
                    </div>
                  </div>
                )}

                {selectedBuilding === 'contact' && (
                  <div>
                    <h3>📡 Get In Touch</h3>
                    <p>Let's connect and discuss opportunities, collaborations, or just have a tech conversation!</p>
                    <div className="contact-info">
                      <div className="contact-item">
                        <strong>Email:</strong> <EMAIL>
                      </div>
                      <div className="contact-item">
                        <strong>LinkedIn:</strong> linkedin.com/in/vinayakbs
                      </div>
                      <div className="contact-item">
                        <strong>GitHub:</strong> github.com/vinayakbs
                      </div>
                      <div className="contact-item">
                        <strong>Location:</strong> Dharwad, Karnataka, India
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </main>
    </div>
  )
}

export default App
