import { useRef, useState, useEffect } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Text, Box, Sphere } from '@react-three/drei'
import { useSpring, animated } from '@react-spring/web'
import * as THREE from 'three'

function LoadingCube() {
  const meshRef = useRef()
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.7
      meshRef.current.rotation.z = state.clock.elapsedTime * 0.3
      
      // Pulsing effect
      const scale = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.2
      meshRef.current.scale.setScalar(scale)
    }
  })

  return (
    <Box ref={meshRef} args={[1, 1, 1]}>
      <meshStandardMaterial 
        color="#00f5ff"
        transparent
        opacity={0.8}
        wireframe
        emissive="#00f5ff"
        emissiveIntensity={0.2}
      />
    </Box>
  )
}

function LoadingRings() {
  const ringsRef = useRef()
  
  useFrame((state) => {
    if (ringsRef.current) {
      ringsRef.current.children.forEach((ring, i) => {
        ring.rotation.x = state.clock.elapsedTime * (i + 1) * 0.5
        ring.rotation.y = state.clock.elapsedTime * (i + 1) * 0.3
        ring.rotation.z = state.clock.elapsedTime * (i + 1) * 0.7
      })
    }
  })

  return (
    <group ref={ringsRef}>
      {[2, 3, 4].map((radius, i) => (
        <mesh key={i}>
          <torusGeometry args={[radius, 0.1, 8, 32]} />
          <meshBasicMaterial 
            color={['#00f5ff', '#8b5cf6', '#10b981'][i]}
            transparent
            opacity={0.6}
          />
        </mesh>
      ))}
    </group>
  )
}

function LoadingParticles() {
  const particlesRef = useRef()
  
  const particleCount = 100
  const positions = new Float32Array(particleCount * 3)
  
  for (let i = 0; i < particleCount * 3; i += 3) {
    positions[i] = (Math.random() - 0.5) * 20
    positions[i + 1] = (Math.random() - 0.5) * 20
    positions[i + 2] = (Math.random() - 0.5) * 20
  }

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.2
      
      const positions = particlesRef.current.geometry.attributes.position.array
      for (let i = 1; i < positions.length; i += 3) {
        positions[i] = Math.sin(state.clock.elapsedTime + positions[i]) * 2
      }
      particlesRef.current.geometry.attributes.position.needsUpdate = true
    }
  })

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        color="#00f5ff"
        size={0.1}
        transparent
        opacity={0.8}
        sizeAttenuation
      />
    </points>
  )
}

function ProgressBar3D({ progress }) {
  const barRef = useRef()
  
  useFrame(() => {
    if (barRef.current) {
      barRef.current.scale.x = progress / 100
    }
  })

  return (
    <group position={[0, -3, 0]}>
      {/* Background bar */}
      <Box args={[6, 0.2, 0.1]} position={[0, 0, 0]}>
        <meshBasicMaterial color="#1a1a2e" transparent opacity={0.5} />
      </Box>
      
      {/* Progress bar */}
      <Box ref={barRef} args={[6, 0.2, 0.1]} position={[-3 + (progress / 100) * 3, 0, 0.05]}>
        <meshBasicMaterial 
          color="#00f5ff"
          emissive="#00f5ff"
          emissiveIntensity={0.3}
        />
      </Box>
      
      {/* Progress text */}
      <Text
        position={[0, -0.8, 0]}
        fontSize={0.4}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        {Math.round(progress)}%
      </Text>
    </group>
  )
}

function LoadingText({ text }) {
  const textRef = useRef()
  
  useFrame((state) => {
    if (textRef.current) {
      textRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.2
    }
  })

  return (
    <Text
      ref={textRef}
      position={[0, 2, 0]}
      fontSize={0.8}
      color="#00f5ff"
      anchorX="center"
      anchorY="middle"
      font="/fonts/inter-bold.woff"
    >
      {text}
    </Text>
  )
}

const LoadingScreen = ({ isLoading, progress = 0, onComplete }) => {
  const [loadingText, setLoadingText] = useState('INITIALIZING')
  const [currentProgress, setCurrentProgress] = useState(0)

  const springProps = useSpring({
    opacity: isLoading ? 1 : 0,
    transform: isLoading ? 'scale(1)' : 'scale(0.8)',
    config: { tension: 280, friction: 60 }
  })

  useEffect(() => {
    if (!isLoading) return

    const texts = [
      'INITIALIZING',
      'LOADING ASSETS',
      'BUILDING UNIVERSE',
      'CONNECTING NODES',
      'OPTIMIZING PERFORMANCE',
      'READY TO LAUNCH'
    ]

    const interval = setInterval(() => {
      setCurrentProgress(prev => {
        const newProgress = Math.min(prev + Math.random() * 15, 100)
        
        const textIndex = Math.floor((newProgress / 100) * texts.length)
        setLoadingText(texts[Math.min(textIndex, texts.length - 1)])
        
        if (newProgress >= 100) {
          setTimeout(() => {
            onComplete?.()
          }, 1000)
        }
        
        return newProgress
      })
    }, 200)

    return () => clearInterval(interval)
  }, [isLoading, onComplete])

  if (!isLoading && springProps.opacity.get() === 0) return null

  return (
    <animated.div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...springProps
      }}
    >
      <div style={{ width: '100%', height: '100%', position: 'relative' }}>
        <Canvas camera={{ position: [0, 0, 8], fov: 60 }}>
          <ambientLight intensity={0.4} />
          <pointLight position={[10, 10, 10]} intensity={1} color="#00f5ff" />
          <pointLight position={[-10, -10, -10]} intensity={0.5} color="#8b5cf6" />
          
          <LoadingCube />
          <LoadingRings />
          <LoadingParticles />
          <LoadingText text={loadingText} />
          <ProgressBar3D progress={currentProgress} />
        </Canvas>
        
        {/* Additional UI elements */}
        <div 
          style={{
            position: 'absolute',
            bottom: '50px',
            left: '50%',
            transform: 'translateX(-50%)',
            color: '#00f5ff',
            fontSize: '14px',
            fontFamily: 'Inter, sans-serif',
            textAlign: 'center'
          }}
        >
          <div style={{ marginBottom: '10px' }}>
            Powered by Three.js & React Three Fiber
          </div>
          <div style={{ opacity: 0.7 }}>
            Building immersive experiences...
          </div>
        </div>
      </div>
    </animated.div>
  )
}

export default LoadingScreen
