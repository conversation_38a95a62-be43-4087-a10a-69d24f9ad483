import { useEffect, useRef } from 'react'
import * as THREE from 'three'

const ParticleSystem = ({ 
  particleCount = 1000, 
  color = '#00f5ff', 
  size = 2, 
  speed = 0.5,
  interactive = false 
}) => {
  const containerRef = useRef()
  const sceneRef = useRef()
  const rendererRef = useRef()
  const particlesRef = useRef()
  const mouseRef = useRef({ x: 0, y: 0 })

  useEffect(() => {
    if (!containerRef.current) return

    // Scene setup
    const scene = new THREE.Scene()
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
    const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true })
    
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setClearColor(0x000000, 0)
    containerRef.current.appendChild(renderer.domElement)

    // Particle geometry
    const geometry = new THREE.BufferGeometry()
    const positions = new Float32Array(particleCount * 3)
    const velocities = new Float32Array(particleCount * 3)
    const colors = new Float32Array(particleCount * 3)

    for (let i = 0; i < particleCount * 3; i += 3) {
      // Position
      positions[i] = (Math.random() - 0.5) * 2000
      positions[i + 1] = (Math.random() - 0.5) * 2000
      positions[i + 2] = (Math.random() - 0.5) * 2000

      // Velocity
      velocities[i] = (Math.random() - 0.5) * speed
      velocities[i + 1] = (Math.random() - 0.5) * speed
      velocities[i + 2] = (Math.random() - 0.5) * speed

      // Colors
      const colorObj = new THREE.Color(color)
      colors[i] = colorObj.r + Math.random() * 0.2
      colors[i + 1] = colorObj.g + Math.random() * 0.2
      colors[i + 2] = colorObj.b + Math.random() * 0.2
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))

    // Particle material
    const material = new THREE.PointsMaterial({
      size: size,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending,
      sizeAttenuation: true
    })

    // Create particle system
    const particles = new THREE.Points(geometry, material)
    scene.add(particles)

    camera.position.z = 1000

    sceneRef.current = scene
    rendererRef.current = renderer
    particlesRef.current = { particles, velocities, positions }

    // Mouse interaction
    const handleMouseMove = (event) => {
      mouseRef.current.x = (event.clientX / window.innerWidth) * 2 - 1
      mouseRef.current.y = -(event.clientY / window.innerHeight) * 2 + 1
    }

    if (interactive) {
      window.addEventListener('mousemove', handleMouseMove)
    }

    // Animation loop
    const animate = () => {
      requestAnimationFrame(animate)

      if (particlesRef.current) {
        const { particles, velocities, positions } = particlesRef.current
        const positionAttribute = particles.geometry.attributes.position

        for (let i = 0; i < particleCount * 3; i += 3) {
          // Update positions
          positions[i] += velocities[i]
          positions[i + 1] += velocities[i + 1]
          positions[i + 2] += velocities[i + 2]

          // Interactive mouse effect
          if (interactive) {
            const mouseInfluence = 50
            const dx = mouseRef.current.x * 1000 - positions[i]
            const dy = mouseRef.current.y * 1000 - positions[i + 1]
            const distance = Math.sqrt(dx * dx + dy * dy)
            
            if (distance < mouseInfluence) {
              const force = (mouseInfluence - distance) / mouseInfluence
              velocities[i] += dx * force * 0.001
              velocities[i + 1] += dy * force * 0.001
            }
          }

          // Boundary wrapping
          if (positions[i] > 1000) positions[i] = -1000
          if (positions[i] < -1000) positions[i] = 1000
          if (positions[i + 1] > 1000) positions[i + 1] = -1000
          if (positions[i + 1] < -1000) positions[i + 1] = 1000
          if (positions[i + 2] > 1000) positions[i + 2] = -1000
          if (positions[i + 2] < -1000) positions[i + 2] = 1000

          // Damping
          velocities[i] *= 0.99
          velocities[i + 1] *= 0.99
          velocities[i + 2] *= 0.99
        }

        positionAttribute.needsUpdate = true
        particles.rotation.y += 0.001
      }

      renderer.render(scene, camera)
    }

    animate()

    // Resize handler
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight
      camera.updateProjectionMatrix()
      renderer.setSize(window.innerWidth, window.innerHeight)
    }

    window.addEventListener('resize', handleResize)

    // Cleanup
    return () => {
      if (interactive) {
        window.removeEventListener('mousemove', handleMouseMove)
      }
      window.removeEventListener('resize', handleResize)
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement)
      }
      renderer.dispose()
      geometry.dispose()
      material.dispose()
    }
  }, [particleCount, color, size, speed, interactive])

  return (
    <div 
      ref={containerRef} 
      className="particle-system"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -2,
        pointerEvents: 'none'
      }}
    />
  )
}

export default ParticleSystem
