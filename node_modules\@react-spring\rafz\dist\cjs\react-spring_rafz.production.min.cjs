"use strict";var T=Object.defineProperty;var S=Object.getOwnPropertyDescriptor;var U=Object.getOwnPropertyNames;var g=Object.prototype.hasOwnProperty;var E=(e,t)=>{for(var a in t)T(e,a,{get:t[a],enumerable:!0})},L=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of U(t))!g.call(e,r)&&r!==a&&T(e,r,{get:()=>t[r],enumerable:!(o=S(t,r))||o.enumerable});return e};var A=e=>L(T({},"__esModule",{value:!0}),e);var N={};E(N,{__raf:()=>I,raf:()=>n});module.exports=A(N);var f=l(),n=e=>s(e,f),m=l();n.write=e=>s(e,m);var c=l();n.onStart=e=>s(e,c);var h=l();n.onFrame=e=>s(e,h);var p=l();n.onFinish=e=>s(e,p);var d=[];n.setTimeout=(e,t)=>{let a=n.now()+t,o=()=>{let F=d.findIndex(R=>R.cancel==o);~F&&d.splice(F,1),i-=~F?1:0},r={time:a,handler:e,cancel:o};return d.splice(v(a),0,r),i+=1,b(),r};var v=e=>~(~d.findIndex(t=>t.time>e)||~d.length);n.cancel=e=>{c.delete(e),h.delete(e),p.delete(e),f.delete(e),m.delete(e)};n.sync=e=>{y=!0,n.batchedUpdates(e),y=!1};n.throttle=e=>{let t;function a(){try{e(...t)}finally{t=null}}function o(...r){t=r,n.onStart(a)}return o.handler=e,o.cancel=()=>{c.delete(a),t=null},o};var w=typeof window<"u"?window.requestAnimationFrame:()=>{};n.use=e=>w=e;n.now=typeof performance<"u"?()=>performance.now():Date.now;n.batchedUpdates=e=>e();n.catch=console.error;n.frameLoop="always";n.advance=()=>{n.frameLoop!=="demand"?console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"):Q()};var u=-1,i=0,y=!1;function s(e,t){y?(t.delete(e),e(0)):(t.add(e),b())}function b(){u<0&&(u=0,n.frameLoop!=="demand"&&w(x))}function C(){u=-1}function x(){~u&&(w(x),n.batchedUpdates(Q))}function Q(){let e=u;u=n.now();let t=v(u);if(t&&(z(d.splice(0,t),a=>a.handler()),i-=t),!i){C();return}c.flush(),f.flush(e?Math.min(64,u-e):16.667),h.flush(),m.flush(),p.flush()}function l(){let e=new Set,t=e;return{add(a){i+=t==e&&!e.has(a)?1:0,e.add(a)},delete(a){return i-=t==e&&e.has(a)?1:0,e.delete(a)},flush(a){t.size&&(e=new Set,i-=t.size,z(t,o=>o(a)&&e.add(o)),i+=e.size,t=e)}}}function z(e,t){e.forEach(a=>{try{t(a)}catch(o){n.catch(o)}})}var I={count(){return i},isRunning(){return u>=0},clear(){u=-1,d=[],c=l(),f=l(),h=l(),m=l(),p=l(),i=0}};0&&(module.exports={__raf,raf});
