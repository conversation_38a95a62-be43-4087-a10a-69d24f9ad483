/* Loading Screen */
.loading-screen {
  min-height: 100vh;
  background: var(--dark-bg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
}

.loading-title {
  font-size: 4rem;
  font-weight: 900;
  font-family: 'Orbitron', monospace;
  color: var(--cyber-blue);
  text-shadow: 0 0 20px var(--cyber-blue);
  margin-bottom: 1rem;
}

.loading-subtitle {
  font-size: 1.25rem;
  color: var(--cyber-purple);
  animation: pulse 2s infinite;
  margin-bottom: 2rem;
}

.loading-bar-container {
  width: 16rem;
  height: 0.5rem;
  background: var(--dark-surface);
  border-radius: 9999px;
  margin: 0 auto;
  overflow: hidden;
}

.loading-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--cyber-blue), var(--cyber-purple));
  border-radius: 9999px;
}

/* App Container */
.app-container {
  min-height: 100vh;
  background: var(--dark-bg);
  color: white;
  overflow: hidden;
}

/* Header */
.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(0, 245, 255, 0.2);
}

.header-content {
  max-width: 80rem;
  margin: 0 auto;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Orbitron', monospace;
  color: var(--cyber-blue);
  text-shadow: 0 0 10px var(--cyber-blue);
}

.subtitle {
  font-size: 0.875rem;
  color: #9ca3af;
}

.nav-container {
  display: flex;
  gap: 1.5rem;
}

.nav-button {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid transparent;
  color: #d1d5db;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.nav-button:hover {
  color: var(--cyber-blue);
  background: rgba(0, 245, 255, 0.1);
  border-color: rgba(0, 245, 255, 0.3);
}

.nav-button.active {
  color: var(--cyber-blue);
  background: rgba(0, 245, 255, 0.2);
  border-color: rgba(0, 245, 255, 0.5);
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

/* Main Content */
.main-content {
  padding-top: 5rem;
  position: relative;
  min-height: 100vh;
}

/* City Container */
.city-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1500px;
  background:
    radial-gradient(circle at 25% 25%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

.city-grid {
  display: grid;
  grid-template-columns: repeat(4, 120px);
  grid-template-rows: repeat(4, 120px);
  gap: 2rem;
  transform: rotateX(60deg) rotateY(-45deg);
  transform-style: preserve-3d;
}

/* Buildings */
.building {
  position: relative;
  width: 80px;
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
  transform-style: preserve-3d;
}

.building:hover {
  transform: translateY(-10px) scale(1.05);
  filter: brightness(1.2);
}

.building-content {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 245, 255, 0.3), rgba(0, 245, 255, 0.1));
  border: 1px solid rgba(0, 245, 255, 0.5);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.building-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 8px,
      rgba(0, 245, 255, 0.1) 8px,
      rgba(0, 245, 255, 0.1) 10px
    );
}

.building-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  z-index: 1;
}

.building-label {
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  z-index: 1;
  color: white;
}

/* Building Types */
.react-building .building-content {
  background: linear-gradient(135deg, rgba(0, 245, 255, 0.4), rgba(0, 245, 255, 0.1));
  border-color: var(--cyber-blue);
  height: 80px;
}

.node-building .building-content {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.4), rgba(16, 185, 129, 0.1));
  border-color: var(--cyber-green);
  height: 100px;
}

.mongodb-building .building-content {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.4), rgba(139, 92, 246, 0.1));
  border-color: var(--cyber-purple);
  height: 120px;
}

.project-building .building-content {
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.4), rgba(249, 115, 22, 0.1));
  border-color: var(--cyber-orange);
  height: 110px;
}

.project-building.tall .building-content {
  height: 150px;
}

.project-building.medium .building-content {
  height: 130px;
}

.education-building .building-content {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.4), rgba(251, 191, 36, 0.1));
  border-color: var(--neon-yellow);
  height: 140px;
  clip-path: polygon(20% 0%, 80% 0%, 100% 20%, 100% 80%, 80% 100%, 20% 100%, 0% 80%, 0% 20%);
}

.contact-building .building-content {
  background: linear-gradient(135deg, rgba(0, 245, 255, 0.4), rgba(0, 245, 255, 0.1));
  border-color: var(--cyber-blue);
  height: 180px;
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.javascript-building .building-content {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.4), rgba(251, 191, 36, 0.1));
  border-color: var(--neon-yellow);
  height: 75px;
  clip-path: polygon(0 0, 100% 0, 100% 85%, 50% 100%, 0 85%);
}

.python-building .building-content {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.4), rgba(16, 185, 129, 0.1));
  border-color: var(--cyber-green);
  height: 85px;
}

.cpp-building .building-content {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.4), rgba(139, 92, 246, 0.1));
  border-color: var(--cyber-purple);
  height: 90px;
}

.firebase-building .building-content {
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.4), rgba(249, 115, 22, 0.1));
  border-color: var(--cyber-orange);
  height: 95px;
  border-radius: 8px 8px 0 0;
}

.git-building .building-content {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.4), rgba(139, 92, 246, 0.1));
  border-color: var(--cyber-purple);
  height: 70px;
}

.achievement-building .building-content {
  background: linear-gradient(135deg, rgba(244, 114, 182, 0.4), rgba(244, 114, 182, 0.1));
  border-color: var(--cyber-pink);
  height: 60px;
}

.empty-space {
  visibility: hidden;
}

/* Data Flows */
.data-flows {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.data-line {
  position: absolute;
  height: 2px;
  opacity: 0.7;
}

/* Horizontal lines */
.data-line.horizontal.line-1 {
  top: 25%;
  left: 0;
  right: 0;
  background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
  animation: dataFlowHorizontal 4s linear infinite;
}

.data-line.horizontal.line-2 {
  top: 50%;
  left: 0;
  right: 0;
  background: linear-gradient(90deg, transparent, var(--cyber-green), transparent);
  animation: dataFlowHorizontal 4s linear infinite;
  animation-delay: 1s;
}

.data-line.horizontal.line-3 {
  top: 75%;
  left: 0;
  right: 0;
  background: linear-gradient(90deg, transparent, var(--cyber-purple), transparent);
  animation: dataFlowHorizontal 4s linear infinite;
  animation-delay: 2s;
}

/* Vertical lines */
.data-line.vertical.line-4 {
  left: 25%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(0deg, transparent, var(--cyber-orange), transparent);
  animation: dataFlowVertical 3s linear infinite;
  animation-delay: 0.5s;
}

.data-line.vertical.line-5 {
  left: 75%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(0deg, transparent, var(--cyber-pink), transparent);
  animation: dataFlowVertical 3s linear infinite;
  animation-delay: 2.5s;
}

/* Diagonal lines */
.data-line.diagonal.line-6 {
  top: 0;
  left: 0;
  width: 141.42%; /* sqrt(2) * 100% for diagonal */
  height: 2px;
  transform: rotate(45deg);
  transform-origin: 0 0;
  background: linear-gradient(90deg, transparent, var(--neon-yellow), transparent);
  animation: dataFlowDiagonal 5s linear infinite;
  animation-delay: 1.5s;
}

.data-line.diagonal.line-7 {
  top: 0;
  right: 0;
  width: 141.42%;
  height: 2px;
  transform: rotate(-45deg);
  transform-origin: 100% 0;
  background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
  animation: dataFlowDiagonal 5s linear infinite;
  animation-delay: 3s;
}

/* Data packets */
.data-packets {
  position: absolute;
  inset: 0;
}

.data-packet {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--cyber-blue);
  border-radius: 50%;
  box-shadow: 0 0 8px var(--cyber-blue);
}

/* Network pulse */
.network-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  border: 2px solid var(--cyber-blue);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* Modal */
.modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-content {
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(0, 245, 255, 0.3);
  border-radius: 0.75rem;
  padding: 2rem;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: transparent;
  border: none;
  color: #9ca3af;
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.modal-close:hover {
  color: white;
}

.modal-body h3 {
  color: var(--cyber-blue);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-family: 'Orbitron', monospace;
}

.modal-body p {
  color: #d1d5db;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.skills-list, .tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.skills-list span, .tech-stack span {
  padding: 0.25rem 0.75rem;
  background: rgba(0, 245, 255, 0.2);
  color: var(--cyber-blue);
  border: 1px solid rgba(0, 245, 255, 0.3);
  border-radius: 9999px;
  font-size: 0.875rem;
}

.project-features h4, .education-details h4, .academic-performance h4, .institution-info h4 {
  color: var(--cyber-purple);
  font-size: 1.125rem;
  margin: 1rem 0 0.5rem 0;
  font-weight: 600;
}

.project-features ul, .education-details ul, .academic-performance ul, .institution-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.project-features li, .education-details li, .academic-performance li, .institution-info li {
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #d1d5db;
}

.project-features li:before, .education-details li:before, .academic-performance li:before, .institution-info li:before {
  content: "▶ ";
  color: var(--cyber-blue);
  margin-right: 0.5rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  padding: 1rem;
  background: rgba(0, 245, 255, 0.1);
  border: 1px solid rgba(0, 245, 255, 0.3);
  border-radius: 0.5rem;
  color: #d1d5db;
}

.contact-item strong {
  color: var(--cyber-blue);
  margin-right: 0.5rem;
}

.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.achievement-item {
  padding: 1rem;
  background: rgba(244, 114, 182, 0.1);
  border: 1px solid rgba(244, 114, 182, 0.3);
  border-radius: 0.5rem;
  border-left: 4px solid var(--cyber-pink);
}

.achievement-item h4 {
  color: var(--cyber-pink);
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.achievement-item p {
  color: #d1d5db;
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.education-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .education-details {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes dataFlowHorizontal {
  0% {
    background: linear-gradient(90deg, transparent, transparent, transparent);
  }
  25% {
    background: linear-gradient(90deg, transparent, currentColor, transparent);
  }
  50% {
    background: linear-gradient(90deg, transparent, transparent, currentColor);
  }
  75% {
    background: linear-gradient(90deg, currentColor, transparent, transparent);
  }
  100% {
    background: linear-gradient(90deg, transparent, transparent, transparent);
  }
}

@keyframes dataFlowVertical {
  0% {
    background: linear-gradient(0deg, transparent, transparent, transparent);
  }
  25% {
    background: linear-gradient(0deg, transparent, currentColor, transparent);
  }
  50% {
    background: linear-gradient(0deg, transparent, transparent, currentColor);
  }
  75% {
    background: linear-gradient(0deg, currentColor, transparent, transparent);
  }
  100% {
    background: linear-gradient(0deg, transparent, transparent, transparent);
  }
}

@keyframes dataFlowDiagonal {
  0% {
    background: linear-gradient(90deg, transparent, transparent, transparent);
    opacity: 0;
  }
  20% {
    background: linear-gradient(90deg, transparent, currentColor, transparent);
    opacity: 1;
  }
  40% {
    background: linear-gradient(90deg, transparent, transparent, currentColor);
    opacity: 1;
  }
  60% {
    background: linear-gradient(90deg, currentColor, transparent, transparent);
    opacity: 1;
  }
  80% {
    background: linear-gradient(90deg, transparent, transparent, transparent);
    opacity: 0;
  }
  100% {
    background: linear-gradient(90deg, transparent, transparent, transparent);
    opacity: 0;
  }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px currentColor; }
  100% { box-shadow: 0 0 20px currentColor, 0 0 30px currentColor; }
}

/* Controls */
.controls-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 40;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-button {
  width: 3rem;
  height: 3rem;
  background: rgba(0, 245, 255, 0.2);
  border: 1px solid rgba(0, 245, 255, 0.5);
  border-radius: 50%;
  color: var(--cyber-blue);
  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
}

.control-button:hover {
  background: rgba(0, 245, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 245, 255, 0.5);
  transform: scale(1.1);
}

.control-button.reset {
  background: rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.5);
  color: var(--cyber-purple);
}

.control-button.reset:hover {
  background: rgba(139, 92, 246, 0.3);
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

/* Mini Map */
.mini-map {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  z-index: 40;
  width: 12rem;
  height: 8rem;
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(0, 245, 255, 0.3);
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.mini-map-title {
  font-size: 0.75rem;
  color: var(--cyber-blue);
  font-family: 'Orbitron', monospace;
  margin-bottom: 0.5rem;
  text-align: center;
}

.mini-map-content {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--dark-bg);
  border-radius: 0.25rem;
  overflow: hidden;
}

.mini-map-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
  width: 100%;
  height: 100%;
  gap: 1px;
}

.mini-map-cell {
  background: rgba(0, 245, 255, 0.3);
  transition: all 0.3s ease;
}

.mini-map-cell.active {
  background: rgba(139, 92, 246, 0.6);
  box-shadow: 0 0 4px rgba(139, 92, 246, 0.8);
}

.mini-map-viewport {
  position: absolute;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--cyber-green);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* Enhanced Building Hover Effects */
.building:hover .building-content {
  animation: buildingPulse 1s ease-in-out infinite alternate;
}

@keyframes buildingPulse {
  0% {
    box-shadow: 0 0 10px currentColor;
    transform: translateZ(0);
  }
  100% {
    box-shadow: 0 0 25px currentColor, 0 0 35px currentColor;
    transform: translateZ(10px);
  }
}

/* Smooth transitions for city navigation */
.city-container {
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Day/Night Mode */
.city-container.day-mode {
  background:
    radial-gradient(circle at 25% 25%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

.city-container.night-mode {
  background:
    radial-gradient(circle at 25% 25%, rgba(139, 92, 246, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(0, 245, 255, 0.2) 0%, transparent 50%),
    linear-gradient(135deg, rgba(10, 10, 10, 0.8) 0%, rgba(26, 26, 46, 0.9) 100%);
}

.night-mode .building-content {
  box-shadow: 0 0 15px currentColor;
  filter: brightness(1.3);
}

.night-mode .building-content::before {
  background:
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 6px,
      rgba(255, 255, 255, 0.1) 6px,
      rgba(255, 255, 255, 0.1) 8px
    );
}

/* Weather Effects */
.weather-effect {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 5;
}

.rain-drop {
  position: absolute;
  width: 2px;
  height: 10px;
  background: linear-gradient(to bottom, rgba(0, 245, 255, 0.8), transparent);
  border-radius: 1px;
}

.snow-flake {
  position: absolute;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
}

.fog-effect {
  background:
    radial-gradient(circle at 30% 40%, rgba(200, 200, 200, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 70% 60%, rgba(180, 180, 180, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 50% 80%, rgba(220, 220, 220, 0.25) 0%, transparent 50%);
  backdrop-filter: blur(2px);
}

.weather-clear .building-content {
  filter: brightness(1.1);
}

.weather-rain .building-content {
  filter: brightness(0.8) saturate(1.2);
}

.weather-snow .building-content {
  filter: brightness(1.2) contrast(1.1);
}

.weather-fog .building-content {
  filter: brightness(0.9) blur(0.5px);
}

/* Enhanced Control Buttons */
.control-button.day-night {
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.5);
  color: var(--neon-yellow);
}

.control-button.day-night:hover {
  background: rgba(251, 191, 36, 0.3);
  box-shadow: 0 0 15px rgba(251, 191, 36, 0.5);
}

.control-button.day-night.night {
  background: rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.5);
  color: var(--cyber-purple);
}

.control-button.day-night.night:hover {
  background: rgba(139, 92, 246, 0.3);
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

.control-button.weather {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.5);
  color: var(--cyber-green);
}

.control-button.weather:hover {
  background: rgba(16, 185, 129, 0.3);
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    display: none;
  }

  .city-grid {
    grid-template-columns: repeat(2, 100px);
    gap: 1rem;
    transform: rotateX(45deg) rotateY(-30deg);
  }

  .building {
    width: 60px;
    height: 80px;
  }

  .loading-title {
    font-size: 2.5rem;
  }

  .modal-content {
    margin: 1rem;
    padding: 1.5rem;
  }

  .controls-container {
    bottom: 1rem;
    right: 1rem;
  }

  .mini-map {
    bottom: 1rem;
    left: 1rem;
    width: 10rem;
    height: 6rem;
  }

  .control-button {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
}
