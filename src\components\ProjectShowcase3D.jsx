import { useRef, useState } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Text, Box, Plane } from '@react-three/drei'
import { useSpring, animated } from '@react-spring/three'
import * as THREE from 'three'

function ProjectCard3D({ project, position, index, onHover, isHovered }) {
  const meshRef = useRef()
  const [clicked, setClicked] = useState(false)
  
  const { scale, rotation } = useSpring({
    scale: isHovered ? [1.1, 1.1, 1.1] : [1, 1, 1],
    rotation: isHovered ? [0.1, 0.1, 0] : [0, 0, 0],
    config: { mass: 1, tension: 280, friction: 60 }
  })

  useFrame((state) => {
    if (meshRef.current && !isHovered) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime + index) * 0.1
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index * 0.5) * 0.2
    }
  })

  const handleClick = () => {
    setClicked(!clicked)
    if (project.link) {
      window.open(project.link, '_blank')
    }
  }

  return (
    <animated.group 
      ref={meshRef}
      position={position}
      scale={scale}
      rotation={rotation}
      onPointerOver={() => onHover(index)}
      onPointerOut={() => onHover(null)}
      onClick={handleClick}
    >
      {/* Main card */}
      <Box args={[3, 4, 0.1]}>
        <meshStandardMaterial 
          color={project.color || '#1a1a2e'}
          transparent
          opacity={0.9}
          roughness={0.3}
          metalness={0.1}
        />
      </Box>
      
      {/* Glowing border */}
      <Box args={[3.1, 4.1, 0.05]} position={[0, 0, -0.1]}>
        <meshBasicMaterial 
          color="#00f5ff"
          transparent
          opacity={isHovered ? 0.5 : 0.2}
        />
      </Box>
      
      {/* Project title */}
      <Text
        position={[0, 1.5, 0.1]}
        fontSize={0.3}
        color="white"
        anchorX="center"
        anchorY="middle"
        maxWidth={2.5}
        font="/fonts/inter-bold.woff"
      >
        {project.title}
      </Text>
      
      {/* Project description */}
      <Text
        position={[0, 0.5, 0.1]}
        fontSize={0.15}
        color="#a0a0a0"
        anchorX="center"
        anchorY="middle"
        maxWidth={2.5}
        textAlign="center"
      >
        {project.description}
      </Text>
      
      {/* Tech stack */}
      <Text
        position={[0, -0.5, 0.1]}
        fontSize={0.12}
        color="#00f5ff"
        anchorX="center"
        anchorY="middle"
        maxWidth={2.5}
        textAlign="center"
      >
        {project.tech?.join(' • ')}
      </Text>
      
      {/* Status indicator */}
      <Box args={[0.2, 0.2, 0.05]} position={[1.2, 1.5, 0.1]}>
        <meshBasicMaterial 
          color={project.status === 'completed' ? '#10b981' : '#f97316'}
          emissive={project.status === 'completed' ? '#10b981' : '#f97316'}
          emissiveIntensity={0.3}
        />
      </Box>
      
      {/* Interactive elements */}
      {isHovered && (
        <>
          <Text
            position={[0, -1.5, 0.1]}
            fontSize={0.15}
            color="#8b5cf6"
            anchorX="center"
            anchorY="middle"
          >
            Click to view →
          </Text>
          
          {/* Floating particles around card */}
          {Array.from({ length: 8 }).map((_, i) => {
            const angle = (i / 8) * Math.PI * 2
            const radius = 2
            return (
              <Box 
                key={i}
                args={[0.05, 0.05, 0.05]} 
                position={[
                  Math.cos(angle) * radius,
                  Math.sin(angle) * radius,
                  0.2
                ]}
              >
                <meshBasicMaterial color="#00f5ff" />
              </Box>
            )
          })}
        </>
      )}
    </animated.group>
  )
}

function FloatingBackground() {
  const groupRef = useRef()
  
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.05
    }
  })

  return (
    <group ref={groupRef}>
      {Array.from({ length: 20 }).map((_, i) => {
        const angle = (i / 20) * Math.PI * 2
        const radius = 15 + Math.random() * 10
        const height = (Math.random() - 0.5) * 20
        
        return (
          <Box 
            key={i}
            args={[0.1, 0.1, 0.1]}
            position={[
              Math.cos(angle) * radius,
              height,
              Math.sin(angle) * radius
            ]}
          >
            <meshBasicMaterial 
              color="#8b5cf6" 
              transparent 
              opacity={0.3}
            />
          </Box>
        )
      })}
    </group>
  )
}

function CameraRig({ hoveredIndex }) {
  useFrame((state) => {
    const time = state.clock.elapsedTime
    
    if (hoveredIndex !== null) {
      // Focus on hovered card
      const targetX = Math.sin((hoveredIndex / 6) * Math.PI * 2) * 8
      const targetZ = Math.cos((hoveredIndex / 6) * Math.PI * 2) * 8
      
      state.camera.position.x = THREE.MathUtils.lerp(state.camera.position.x, targetX, 0.05)
      state.camera.position.z = THREE.MathUtils.lerp(state.camera.position.z, targetZ, 0.05)
    } else {
      // Gentle orbit
      state.camera.position.x = Math.sin(time * 0.1) * 12
      state.camera.position.z = Math.cos(time * 0.1) * 12
    }
    
    state.camera.lookAt(0, 0, 0)
  })
  
  return null
}

const ProjectShowcase3D = ({ projects = [] }) => {
  const [hoveredIndex, setHoveredIndex] = useState(null)

  const projectsWithPositions = projects.map((project, index) => {
    const angle = (index / projects.length) * Math.PI * 2
    const radius = 6
    const height = (index % 2) * 2 - 1
    
    return {
      ...project,
      position: [
        Math.cos(angle) * radius,
        height,
        Math.sin(angle) * radius
      ]
    }
  })

  return (
    <div className="project-showcase-3d" style={{ height: '800px', width: '100%' }}>
      <Canvas camera={{ position: [0, 2, 12], fov: 60 }}>
        <ambientLight intensity={0.3} />
        <pointLight position={[10, 10, 10]} intensity={1} color="#00f5ff" />
        <pointLight position={[-10, -10, -10]} intensity={0.5} color="#8b5cf6" />
        <spotLight 
          position={[0, 10, 0]} 
          angle={0.3} 
          penumbra={1} 
          intensity={0.5}
          color="white"
        />
        
        <FloatingBackground />
        
        {projectsWithPositions.map((project, index) => (
          <ProjectCard3D
            key={project.title}
            project={project}
            position={project.position}
            index={index}
            onHover={setHoveredIndex}
            isHovered={hoveredIndex === index}
          />
        ))}
        
        <CameraRig hoveredIndex={hoveredIndex} />
      </Canvas>
    </div>
  )
}

export default ProjectShowcase3D
