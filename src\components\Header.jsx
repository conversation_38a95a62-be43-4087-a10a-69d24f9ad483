import { motion } from 'framer-motion'
import { Github, Linkedin, Mail, MapPin } from 'lucide-react'

const Header = ({ currentSection, setCurrentSection }) => {
  const navItems = [
    { id: 'overview', label: 'Overview', icon: '🏙️' },
    { id: 'skills', label: 'Tech Districts', icon: '⚡' },
    { id: 'projects', label: 'Projects', icon: '🏢' },
    { id: 'education', label: 'Education', icon: '🎓' },
    { id: 'achievements', label: 'Achievements', icon: '🏆' },
    { id: 'contact', label: 'Contact', icon: '📡' }
  ]

  return (
    <motion.header
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className="header-container"
    >
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo/Name */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex items-center space-x-4"
          >
            <div className="text-2xl font-bold font-['Orbitron'] text-cyber-blue glow-blue">
              VINAYAK B S
            </div>
            <div className="text-sm text-gray-400">
              Full Stack Developer
            </div>
          </motion.div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navItems.map((item) => (
              <motion.button
                key={item.id}
                onClick={() => setCurrentSection(item.id)}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                  currentSection === item.id
                    ? 'bg-cyber-blue/20 text-cyber-blue glow-blue'
                    : 'text-gray-300 hover:text-cyber-blue hover:bg-cyber-blue/10'
                }`}
              >
                <span className="text-lg">{item.icon}</span>
                <span className="font-medium">{item.label}</span>
              </motion.button>
            ))}
          </nav>

          {/* Social Links */}
          <div className="flex items-center space-x-4">
            <motion.a
              href="https://github.com/vinayakbs"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.2, rotate: 360 }}
              className="text-gray-400 hover:text-cyber-blue transition-colors"
            >
              <Github size={20} />
            </motion.a>
            <motion.a
              href="https://linkedin.com/in/vinayakbs"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.2, rotate: 360 }}
              className="text-gray-400 hover:text-cyber-blue transition-colors"
            >
              <Linkedin size={20} />
            </motion.a>
            <motion.a
              href="mailto:<EMAIL>"
              whileHover={{ scale: 1.2, rotate: 360 }}
              className="text-gray-400 hover:text-cyber-blue transition-colors"
            >
              <Mail size={20} />
            </motion.a>
          </div>
        </div>

        {/* Mobile Navigation */}
        <motion.nav
          initial={{ height: 0, opacity: 0 }}
          animate={{ 
            height: currentSection ? 'auto' : 0, 
            opacity: currentSection ? 1 : 0 
          }}
          className="md:hidden mt-4 overflow-hidden"
        >
          <div className="grid grid-cols-2 gap-2">
            {navItems.map((item) => (
              <motion.button
                key={item.id}
                onClick={() => setCurrentSection(item.id)}
                whileTap={{ scale: 0.95 }}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300 ${
                  currentSection === item.id
                    ? 'bg-cyber-blue/20 text-cyber-blue'
                    : 'text-gray-300 hover:text-cyber-blue hover:bg-cyber-blue/10'
                }`}
              >
                <span>{item.icon}</span>
                <span className="text-sm">{item.label}</span>
              </motion.button>
            ))}
          </div>
        </motion.nav>
      </div>
    </motion.header>
  )
}

export default Header
