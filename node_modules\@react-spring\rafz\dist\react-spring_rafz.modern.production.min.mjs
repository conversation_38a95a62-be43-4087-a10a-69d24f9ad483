var f=r(),n=e=>c(e,f),m=r();n.write=e=>c(e,m);var d=r();n.onStart=e=>c(e,d);var h=r();n.onFrame=e=>c(e,h);var p=r();n.onFinish=e=>c(e,p);var i=[];n.setTimeout=(e,t)=>{let a=n.now()+t,o=()=>{let F=i.findIndex(z=>z.cancel==o);~F&&i.splice(F,1),u-=~F?1:0},s={time:a,handler:e,cancel:o};return i.splice(w(a),0,s),u+=1,v(),s};var w=e=>~(~i.findIndex(t=>t.time>e)||~i.length);n.cancel=e=>{d.delete(e),h.delete(e),p.delete(e),f.delete(e),m.delete(e)};n.sync=e=>{T=!0,n.batchedUpdates(e),T=!1};n.throttle=e=>{let t;function a(){try{e(...t)}finally{t=null}}function o(...s){t=s,n.onStart(a)}return o.handler=e,o.cancel=()=>{d.delete(a),t=null},o};var y=typeof window<"u"?window.requestAnimationFrame:()=>{};n.use=e=>y=e;n.now=typeof performance<"u"?()=>performance.now():Date.now;n.batchedUpdates=e=>e();n.catch=console.error;n.frameLoop="always";n.advance=()=>{n.frameLoop!=="demand"?console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"):x()};var l=-1,u=0,T=!1;function c(e,t){T?(t.delete(e),e(0)):(t.add(e),v())}function v(){l<0&&(l=0,n.frameLoop!=="demand"&&y(b))}function R(){l=-1}function b(){~l&&(y(b),n.batchedUpdates(x))}function x(){let e=l;l=n.now();let t=w(l);if(t&&(Q(i.splice(0,t),a=>a.handler()),u-=t),!u){R();return}d.flush(),f.flush(e?Math.min(64,l-e):16.667),h.flush(),m.flush(),p.flush()}function r(){let e=new Set,t=e;return{add(a){u+=t==e&&!e.has(a)?1:0,e.add(a)},delete(a){return u-=t==e&&e.has(a)?1:0,e.delete(a)},flush(a){t.size&&(e=new Set,u-=t.size,Q(t,o=>o(a)&&e.add(o)),u+=e.size,t=e)}}}function Q(e,t){e.forEach(a=>{try{t(a)}catch(o){n.catch(o)}})}var S={count(){return u},isRunning(){return l>=0},clear(){l=-1,i=[],d=r(),f=r(),h=r(),m=r(),p=r(),u=0}};export{S as __raf,n as raf};
